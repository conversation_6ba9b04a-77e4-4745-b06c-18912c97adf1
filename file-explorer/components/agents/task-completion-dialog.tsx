// file-explorer/components/agents/task-completion-dialog.tsx
// ✅ TASK 3.3: TaskCompletionDialog Component - User interface for reviewing and approving task completions

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useDialog } from '@/components/dialogs';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, XCircle, AlertTriangle, FileText, BarChart, Target, Edit } from 'lucide-react';
import { TaskApproval } from './agent-ui-bridge';

// ✅ Task State Interface
export interface TaskState {
  id: string;
  description: string;
  agentId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high';
  createdAt: number;
  updatedAt: number;
}

// ✅ Deliverable Report Interface
export interface DeliverableReport {
  taskId: string;
  agentId: string;
  completedAt: number;
  filesModified: string[];
  filesCreated: string[];
  codeQuality: {
    score: number;
    issues: string[];
    suggestions: string[];
  };
  objectives: {
    id: string;
    description: string;
    completed: boolean;
    evidence?: string;
  }[];
  summary: string;
  timeSpent: number;
  tokensUsed: number;
}

// ✅ Component Props Interface
export interface TaskCompletionDialogProps {
  task: TaskState;
  completionReport: DeliverableReport;
  onApprove: () => void;
  onReject: (feedback: string) => void;
  onModify: (modifications: string[]) => void;
}

// ✅ TaskCompletionDialog Component
export const TaskCompletionDialog: React.FC<TaskCompletionDialogProps> = ({
  task,
  completionReport,
  onApprove,
  onReject,
  onModify
}) => {
  const { openDialog, closeDialog } = useDialog()

  const openTaskCompletionDialog = () => {
    openDialog('task-completion', <TaskCompletionDialogContent
      task={task}
      completionReport={completionReport}
      onApprove={() => {
        onApprove()
        closeDialog('task-completion')
      }}
      onReject={(feedback) => {
        onReject(feedback)
        closeDialog('task-completion')
      }}
      onModify={(modifications) => {
        onModify(modifications)
        closeDialog('task-completion')
      }}
      onClose={() => closeDialog('task-completion')}
    />, {
      size: 'xl',
      closable: true,
      position: 'center'
    })
  }

  return (
    <Button onClick={openTaskCompletionDialog}>
      Review Task Completion
    </Button>
  )
}

interface TaskCompletionDialogContentProps {
  task: TaskState;
  completionReport: DeliverableReport;
  onApprove: () => void;
  onReject: (feedback: string) => void;
  onModify: (modifications: string[]) => void;
  onClose: () => void;
}

function TaskCompletionDialogContent({
  task,
  completionReport,
  onApprove,
  onReject,
  onModify,
  onClose
}: TaskCompletionDialogContentProps) {
  const [feedback, setFeedback] = useState('');
  const [modifications, setModifications] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [newModification, setNewModification] = useState('');

  // ✅ Handle adding modification request
  const handleAddModification = () => {
    if (newModification.trim()) {
      setModifications([...modifications, newModification.trim()]);
      setNewModification('');
    }
  };

  // ✅ Handle removing modification request
  const handleRemoveModification = (index: number) => {
    setModifications(modifications.filter((_, i) => i !== index));
  };

  // ✅ Calculate completion percentage
  const completionPercentage = completionReport.objectives.length > 0
    ? Math.round((completionReport.objectives.filter(obj => obj.completed).length / completionReport.objectives.length) * 100)
    : 100;

  // ✅ Get quality color
  const getQualityColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="max-w-4xl max-h-[80vh] overflow-y-auto bg-background rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold flex items-center gap-2">
          <CheckCircle className="h-5 w-5" />
          Task Completion Review
        </h2>
        <p className="text-sm text-muted-foreground">
          Review the task completion and decide whether to approve, reject, or request modifications.
        </p>
      </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="files">Files</TabsTrigger>
            <TabsTrigger value="quality">Quality</TabsTrigger>
            <TabsTrigger value="objectives">Objectives</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            {/* Task Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Task Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Task Description</div>
                    <div className="font-medium">{task.description}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Assigned Agent</div>
                    <div className="font-medium">{task.agentId}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Priority</div>
                    <Badge variant={task.priority === 'high' ? 'destructive' : task.priority === 'medium' ? 'default' : 'secondary'}>
                      {task.priority}
                    </Badge>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Time Spent</div>
                    <div className="font-medium">{Math.round(completionReport.timeSpent / 1000 / 60)} minutes</div>
                  </div>
                </div>
                
                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-2">Completion Summary</div>
                  <div className="p-3 bg-muted rounded-md text-sm">
                    {completionReport.summary}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-blue-600" />
                    <div>
                      <div className="text-sm font-medium">Files Modified</div>
                      <div className="text-2xl font-bold">{completionReport.filesModified.length + completionReport.filesCreated.length}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <BarChart className="h-4 w-4 text-green-600" />
                    <div>
                      <div className="text-sm font-medium">Code Quality</div>
                      <div className={`text-2xl font-bold ${getQualityColor(completionReport.codeQuality.score)}`}>
                        {completionReport.codeQuality.score}%
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-purple-600" />
                    <div>
                      <div className="text-sm font-medium">Objectives</div>
                      <div className="text-2xl font-bold">{completionPercentage}%</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="files" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>File Changes</CardTitle>
                <CardDescription>Files created and modified during task execution</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Files Created */}
                {completionReport.filesCreated.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-green-600 mb-2">Files Created ({completionReport.filesCreated.length})</div>
                    <div className="space-y-1">
                      {completionReport.filesCreated.map((file, index) => (
                        <div key={index} className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-950 rounded-md">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-mono">{file}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Files Modified */}
                {completionReport.filesModified.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-blue-600 mb-2">Files Modified ({completionReport.filesModified.length})</div>
                    <div className="space-y-1">
                      {completionReport.filesModified.map((file, index) => (
                        <div key={index} className="flex items-center gap-2 p-2 bg-blue-50 dark:bg-blue-950 rounded-md">
                          <Edit className="h-4 w-4 text-blue-600" />
                          <span className="text-sm font-mono">{file}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {completionReport.filesCreated.length === 0 && completionReport.filesModified.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No files were created or modified</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="quality" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Code Quality Analysis</CardTitle>
                <CardDescription>Quality metrics and suggestions for the completed work</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Quality Score */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Overall Quality Score</span>
                    <span className={getQualityColor(completionReport.codeQuality.score)}>
                      {completionReport.codeQuality.score}%
                    </span>
                  </div>
                  <Progress value={completionReport.codeQuality.score} className="h-3" />
                </div>

                {/* Issues */}
                {completionReport.codeQuality.issues.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-red-600 mb-2">Issues Found</div>
                    <div className="space-y-2">
                      {completionReport.codeQuality.issues.map((issue, index) => (
                        <div key={index} className="flex items-start gap-2 p-2 bg-red-50 dark:bg-red-950 rounded-md">
                          <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
                          <span className="text-sm">{issue}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Suggestions */}
                {completionReport.codeQuality.suggestions.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-blue-600 mb-2">Suggestions</div>
                    <div className="space-y-2">
                      {completionReport.codeQuality.suggestions.map((suggestion, index) => (
                        <div key={index} className="flex items-start gap-2 p-2 bg-blue-50 dark:bg-blue-950 rounded-md">
                          <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5" />
                          <span className="text-sm">{suggestion}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="objectives" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Objective Completion</CardTitle>
                <CardDescription>Review how well the task objectives were met</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Progress Overview */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Completion Progress</span>
                    <span>{completionPercentage}%</span>
                  </div>
                  <Progress value={completionPercentage} className="h-3" />
                </div>

                {/* Objectives List */}
                <div className="space-y-3">
                  {completionReport.objectives.map((objective) => (
                    <div key={objective.id} className="flex items-start gap-3 p-3 border rounded-md">
                      <div className="flex-shrink-0 mt-0.5">
                        {objective.completed ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-sm">{objective.description}</div>
                        {objective.evidence && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Evidence: {objective.evidence}
                          </div>
                        )}
                      </div>
                      <Badge variant={objective.completed ? 'default' : 'destructive'}>
                        {objective.completed ? 'Complete' : 'Incomplete'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Feedback and Modifications Section */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Review Decision</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Feedback Textarea */}
              <div>
                <label className="text-sm font-medium">Feedback (optional)</label>
                <Textarea
                  placeholder="Provide feedback about the task completion..."
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  rows={3}
                  className="mt-1"
                />
              </div>

              {/* Modification Requests */}
              <div>
                <label className="text-sm font-medium">Modification Requests</label>
                <div className="space-y-2 mt-1">
                  {modifications.map((mod, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-muted rounded-md">
                      <span className="flex-1 text-sm">{mod}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveModification(index)}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                  <div className="flex gap-2">
                    <Textarea
                      placeholder="Add a modification request..."
                      value={newModification}
                      onChange={(e) => setNewModification(e.target.value)}
                      rows={2}
                      className="flex-1"
                    />
                    <Button onClick={handleAddModification} disabled={!newModification.trim()}>
                      Add
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={() => onReject(feedback)}
          >
            <XCircle className="h-4 w-4 mr-2" />
            Reject
          </Button>
          <Button
            variant="secondary"
            onClick={() => onModify(modifications)}
            disabled={modifications.length === 0}
          >
            <Edit className="h-4 w-4 mr-2" />
            Request Modifications
          </Button>
          <Button onClick={onApprove}>
            <CheckCircle className="h-4 w-4 mr-2" />
            Approve & Continue
          </Button>
        </div>
    </div>
  );
};

export default TaskCompletionDialog;
